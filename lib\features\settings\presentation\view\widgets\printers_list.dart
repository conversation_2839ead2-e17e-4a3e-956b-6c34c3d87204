import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/utils/app_colors.dart';
import 'package:point_of_sale/core/utils/app_text_style.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';

import '../../../../../core/utils/size_config.dart';

class PrintersList extends GetView<PrintersIPAddressController> {
  const PrintersList({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.printerIPs.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.print_disabled,
                size: AppSize.height(64),
                color: AppColors.grey,
              ),
              SizedBox(height: AppSize.height(16)),
              Text(
                'noPrintersConfigured'.tr,
                style: AppTextStyle.primary16800.copyWith(
                  color: AppColors.grey,
                ),
              ),
              SizedBox(height: AppSize.height(8)),
              Text(
                'addAPrinterIPAddressAboveToGetStarted'.tr,
                style: AppTextStyle.primary12700.copyWith(
                  color: AppColors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: controller.printerIPs.length,
        itemBuilder: (context, index) {
          final ip = controller.printerIPs[index];
          return Card(
            margin: EdgeInsets.only(bottom: AppSize.height(8)),
            child: ListTile(
              leading: Icon(
                Icons.print,
                color: AppColors.primaryColor,
                size: AppSize.height(24),
              ),
              title: Text(
                ip,
                style: AppTextStyle.primary16800,
              ),
              subtitle: Text(
                'networkPrinter'.tr,
                style: AppTextStyle.primary12700.copyWith(
                  color: AppColors.grey,
                ),
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Icon(
                      Icons.wifi_tethering,
                      color: AppColors.green,
                      size: AppSize.height(20),
                    ),
                    onPressed: () async {
                      await controller.testPrinterConnection(ip);
                    },
                    tooltip: 'testConnection'.tr,
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.delete,
                      color: AppColors.red,
                      size: AppSize.height(20),
                    ),
                    onPressed: () {
                      Get.dialog(
                        AlertDialog(
                          title: Text('deletePrinter'.tr),
                          content: Text(
                              '${'areYouSureYouWantToRemoveThisPrinter'.tr} $ip?'),
                          actions: [
                            TextButton(
                              onPressed: () => Get.back(),
                              child: Text('cancel'.tr),
                            ),
                            TextButton(
                              onPressed: () {
                                controller.removePrinterIP(ip);
                                Get.back();
                              },
                              style: TextButton.styleFrom(
                                foregroundColor: AppColors.red,
                              ),
                              child: Text(
                                'delete'.tr,
                                style: AppTextStyle.red14700,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                    tooltip: 'deletePrinter'.tr,
                  ),
                ],
              ),
            ),
          );
        },
      );
    });
  }
}
