import 'package:get/get.dart';
import 'package:point_of_sale/features/auth/login/presentation/getx/controllers/login_controller.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/create_new_order_controller.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/settings_controller.dart';

import '../../../../../core/services/cash_data_source.dart';
import '../../../../home/<USER>/getx/controllers/order_details_controller.dart';

class PrintController extends GetxController {
  final int orderId = Get.arguments?['orderId'] ?? 0;
  final SettingsController settingsController = Get.find<SettingsController>();
  final int orderNumber =
      int.tryParse(Get.arguments?['orderNumber'].toString() ?? '0') ?? 0;
  final num totalPrice = Get.arguments?['totalPrice'] ?? 0;
  final OrderDetailsController orderDetailsController =
      Get.find<OrderDetailsController>();

  final CreateNewOrderController createNewOrderController =
      Get.find<CreateNewOrderController>();
  final LoginController loginController = Get.find<LoginController>();
  final CashDataSource cashDataSource = Get.put(CashDataSource());

  @override
  void onInit() {
    orderDetailsController.getOrderDetails(orderId);

    super.onInit();
  }

  @override
  void onClose() {
    orderDetailsController.getOrderDetails(orderId);

    super.onDelete();
  }

  RxDouble total = 0.00.obs;
}
