import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/utils/app_colors.dart';
import 'package:point_of_sale/core/utils/app_text_style.dart';
import 'package:point_of_sale/core/utils/size_utils.dart';
import 'package:point_of_sale/features/print/presentation/getx/controllers/print_controller.dart';

import '../../../../../core/utils/size_config.dart';

class PaymentSuccessBox extends GetView<PrintController> {
  const PaymentSuccessBox({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: AppSize.height(170),
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.seafoamBlue,
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            color: AppColors.white,
            size: getSize(100),
          ),
          const SizedBox(width: 16),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Obx(() {
                final vatPercentage = double.parse(controller
                        .loginController
                        .loginTaskModel
                        .data
                        ?.company?[0]
                        .settings
                        ?.invoice?[14]
                        .value ??
                    '0');
                final totalPrice = controller.totalPrice;

                final double displayTotal;
                if (controller.settingsController.addTax.isTrue) {
                  // First equation: Price does NOT include VAT
                  final subtotal = totalPrice;
                  final vatAmount = subtotal * (vatPercentage / 100);
                  displayTotal = subtotal + vatAmount;
                } else {
                  // Second equation: Price INCLUDES VAT - use selling price as is
                  displayTotal = totalPrice;
                }

                return Text(
                  '${displayTotal.toStringAsFixed(2)} ${'SR'.tr}',
                  style: AppTextStyle.white48800,
                );
              }),
              Text(
                'paymentSuccess'.tr,
                style: AppTextStyle.white20800,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
