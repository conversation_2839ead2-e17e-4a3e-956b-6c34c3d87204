name: point_of_sale
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  bidi: ^2.0.13
  board_datetime_picker: ^2.5.0
  cached_network_image: ^3.2.0
  connectivity_plus: ^6.1.4
  cookie_jar: ^4.0.8
  cupertino_icons: ^1.0.8
  dartz: ^0.10.1
  dio: ^5.8.0+1
  dio_web_adapter: ^2.1.1
  esc_pos_utils_plus: ^2.0.4
  flutter:
    sdk: flutter
  flutter_cache_manager: ^3.4.1
  flutter_esc_pos_network: ^1.0.3
  flutter_esc_pos_utils: ^1.0.1
  flutter_launcher_icons: ^0.14.3
  flutter_native_splash: ^2.4.6
  flutter_slidable: ^4.0.0
  flutter_svg: ^2.0.17
  flutter_thermal_printer: ^1.2.1+1
  flutter_time_picker_spinner: ^2.0.0
  get: ^4.6.6
  get_it: ^8.0.3
  get_storage: ^2.1.1
  google_fonts: ^6.2.1
  html: ^0.15.6
  http: ^1.4.0
  image: ^4.5.4
  intl: ^0.20.2
  lottie: ^3.3.1
  multi_dropdown: ^3.0.1
  # numberpicker: ^2.1.2
  network_discovery: ^1.0.0
  network_info_plus: ^6.1.4
  pdf: ^3.11.3
  pin_code_fields: ^8.0.1
  ping_discover_network_plus: ^0.0.5
  pretty_dio_logger: ^1.4.0
  printing: ^5.14.2
  qr_flutter: ^4.1.0
  rename_app: ^1.6.2
  screenshot: ^3.0.0
  shared_preferences: ^2.5.3
  skeletonizer: ^2.0.1
  table_calendar: ^3.2.0
  webview_flutter: ^4.13.0

dev_dependencies:
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/appIcon.png"
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/images/appIcon.png"
  adaptive_icon_foreground_inset: 16
  web:
    generate: true
    image_path: "assets/images/appIcon.png"
flutter_native_splash:
  color: "#ffffff"
  image: assets\images\LogoPOSUBE1.png
  color_dark: "#ffffff"
  image_dark: assets\images\LogoPOSUBE1.png
  branding_dark: assets\images\LogoPOSUBE1.png

  android_12:
    image: assets\images\LogoPOSUBE1.png
    icon_background_color: "#ffffff"
    image_dark: assets\images\LogoPOSUBE1.png
    icon_background_color_dark: "#ffffff"

  web: false
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    # - assets/images/table2
    # - assets/images/table4
    # - assets/images/table6
    # - assets/images/table8
    # - assets/images/table10
    # - assets/images/table12
    - assets/icons/
    - assets/lotties/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Cairo
      fonts:
        - asset: assets\fonts\Cairo-Light.ttf
          weight: 300
        - asset: assets/fonts/Cairo-Regular.ttf
          weight: 400
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
