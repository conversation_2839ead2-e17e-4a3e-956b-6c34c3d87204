name: point_of_sale
description: "A new Flutter project."

publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ^3.6.0

dependencies:
  cached_network_image: ^3.2.0
  cupertino_icons: ^1.0.8
  dartz: ^0.10.1
  dio: ^5.8.0+1
  esc_pos_utils_plus: ^2.0.4
  flutter:
    sdk: flutter
  flutter_esc_pos_network: ^1.0.3
  flutter_esc_pos_utils: ^1.0.1
  flutter_native_splash: ^2.4.6
  flutter_svg: ^2.0.17
  get: ^4.6.6
  get_it: ^8.0.3
  get_storage: ^2.1.1
  google_fonts: ^6.2.1
  http: ^1.4.0
  image: ^4.5.4
  intl: ^0.20.2
  lottie: ^3.3.1
  multi_dropdown: ^3.0.1
  pdf: ^3.11.3
  pin_code_fields: ^8.0.1
  pretty_dio_logger: ^1.4.0
  printing: ^5.14.2
  qr_flutter: ^4.1.0
  rename_app: ^1.6.2
  screenshot: ^3.0.0
  skeletonizer: ^2.0.1
  table_calendar: ^3.2.0

dev_dependencies:
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/appIcon.png"
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/images/appIcon.png"
  adaptive_icon_foreground_inset: 16
  web:
    generate: true
    image_path: "assets/images/appIcon.png"
  windows:
    generate: true
    image_path: "assets/images/appIcon.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/images/appIcon.png"
flutter_native_splash:
  color: "#ffffff"
  image: assets\images\LogoPOSUBE1.png
  color_dark: "#ffffff"
  image_dark: assets\images\LogoPOSUBE1.png
  branding_dark: assets\images\LogoPOSUBE1.png

  android_12:
    image: assets\images\LogoPOSUBE1.png
    icon_background_color: "#ffffff"
    image_dark: assets\images\LogoPOSUBE1.png
    icon_background_color_dark: "#ffffff"

  web: false
flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/lotties/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets\fonts\Cairo-Light.ttf
          weight: 300
        - asset: assets/fonts/Cairo-Regular.ttf
          weight: 400
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
