import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/utils/app_colors.dart';
import 'package:point_of_sale/core/utils/app_text_style.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/settings_controller.dart';

import '../../../../../core/utils/size_config.dart';
import 'add_printer_section.dart';
import 'printers_list.dart';

class SettingsPrintersSection extends GetView<SettingsController> {
  const SettingsPrintersSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        height: controller.addPrinter.isTrue
            ? AppSize.height(500)
            : AppSize.height(50),
        padding: EdgeInsets.all(
          AppSize.height(16),
        ),
        margin: EdgeInsets.symmetric(
          horizontal: AppSize.width(16),
          vertical: AppSize.height(12),
        ),
        decoration: BoxDecoration(
          color: AppColors.primaryWithOpacity,
          borderRadius: BorderRadius.circular(12),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'printerSettings'.tr,
                    style: AppTextStyle.primary18800,
                  ),
                  InkWell(
                    onTap: controller.toggleAddPrinter,
                    borderRadius: BorderRadius.circular(8),
                    child: AnimatedRotation(
                      turns: controller.addPrinter.value ? 0.25 : 0.0,
                      duration: const Duration(milliseconds: 200),
                      child: Icon(
                        Icons.keyboard_arrow_right,
                        color: AppColors.primaryColor,
                        size: AppSize.height(24),
                      ),
                    ),
                  ),
                ],
              ),
              if (controller.addPrinter.value) ...[
                SizedBox(height: AppSize.height(16)),
                Text(
                  'addNewPrinter'.tr,
                  style: AppTextStyle.primary16800.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: AppSize.height(16)),
                const AddPrinterSection(),
                SizedBox(height: AppSize.height(24)),
                Text(
                  'configuredPrinters'.tr,
                  style: AppTextStyle.primary16800.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: AppSize.height(12)),
                const PrintersList(),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
