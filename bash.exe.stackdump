Stack trace:
Frame         Function      Args
0007FFFFAC10  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFAC10, 0007FFFF9B10) msys-2.0.dll+0x1FEBA
0007FFFFAC10  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAEE8) msys-2.0.dll+0x67F9
0007FFFFAC10  000210046832 (000210285FF9, 0007FFFFAAC8, 0007FFFFAC10, 000000000000) msys-2.0.dll+0x6832
0007FFFFAC10  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFAC10  0002100690B4 (0007FFFFAC20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFAEF0  00021006A49D (0007FFFFAC20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE76C30000 ntdll.dll
7FFE76480000 KERNEL32.DLL
7FFE73F80000 KERNELBASE.dll
7FFE76720000 USER32.dll
7FFE746D0000 win32u.dll
7FFE75680000 GDI32.dll
7FFE74430000 gdi32full.dll
7FFE74360000 msvcp_win.dll
7FFE73E60000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE75230000 advapi32.dll
7FFE758D0000 msvcrt.dll
7FFE753B0000 sechost.dll
7FFE74400000 bcrypt.dll
7FFE75A40000 RPCRT4.dll
7FFE735B0000 CRYPTBASE.DLL
7FFE74700000 bcryptPrimitives.dll
7FFE75A00000 IMM32.DLL
