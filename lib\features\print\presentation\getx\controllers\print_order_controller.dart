import 'dart:io';

import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart' as esc_plus;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_esc_pos_network/flutter_esc_pos_network.dart';
import 'package:get/get.dart';
import 'package:image/image.dart' as img;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:point_of_sale/core/widgets/failed_snack_bar.dart';
import 'package:point_of_sale/core/widgets/success_snack_bar.dart';
import 'package:point_of_sale/features/print/presentation/getx/controllers/print_controller.dart';
import 'package:point_of_sale/features/print/presentation/views/print_view.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';
import 'package:printing/printing.dart';
import 'package:screenshot/screenshot.dart';

class PrintOrderController extends GetxController {
  final ScreenshotController screenshotController = ScreenshotController();

  Future<Uint8List> _fetchLogoBytes(String url) async {
    try {
      final File file = await DefaultCacheManager().getSingleFile(url);
      final Uint8List bytes = await file.readAsBytes();
      return bytes;
    } catch (err) {
      throw Exception('Failed to load logo from network: $err');
    }
  }

  /// Generate ESC/POS commands for image-based invoice printing
  Future<List<int>> _generateImageBasedEscPos(Uint8List imageBytes) async {
    try {
      // Initialize generator with 80mm paper size (576 pixels width)
      final profile = await esc_plus.CapabilityProfile.load();
      final generator = esc_plus.Generator(esc_plus.PaperSize.mm80, profile);
      List<int> bytes = [];

      // Decode the image
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        throw Exception('Failed to decode invoice image');
      }

      Get.log('Original image size: ${image.width}x${image.height}');

      // Resize image to fit thermal printer width (576 pixels for 80mm)
      // Maintain aspect ratio
      final maxWidth = 576;
      final aspectRatio = image.height / image.width;
      final targetHeight = (maxWidth * aspectRatio).round();

      final resizedImage = img.copyResize(image,
          width: maxWidth,
          height: targetHeight,
          interpolation: img.Interpolation.linear);

      Get.log(
          'Resized image size: ${resizedImage.width}x${resizedImage.height}');

      // Add the image to the print job
      bytes += generator.image(resizedImage, align: esc_plus.PosAlign.center);

      // Add some space after the image
      bytes += generator.emptyLines(2);

      // Cut the paper
      bytes += generator.cut();

      Get.log('Generated ${bytes.length} ESC/POS bytes from image');
      return bytes;
    } catch (e) {
      Get.log('Error generating image-based ESC/POS: $e');
      rethrow;
    }
  }

  Future<void> printInvoice(PrintController controller) async {
    if (kIsWeb) {
      await _printInvoiceWeb(controller);
      return;
    }
    await _printInvoiceMobile(controller);
  }

  Future<void> _printInvoiceWeb(PrintController controller) async {
    try {
      Get.log('Printing invoice on web platform using image capture');
      await _printInvoiceAsImagePdf(controller);
      successSnackBar(
        'Invoice ready for printing. Please use your browser\'s print dialog.',
      );
    } catch (e) {
      Get.log('Web print error: $e');
      failedSnaskBar(
        'Failed to print invoice: $e',
      );
    }
  }

  Future<void> _printInvoiceAsImagePdf(PrintController controller) async {
    try {
      Get.log('Capturing invoice widget as image for PDF');
      final invoiceWidget = Invoice(controller: controller);

      // PDF printing requires higher resolution for clarity on larger formats.
      // Target width of 1152 (576 * 2) and pixelRatio 6.0 provides good detail for A4.
      final imageBytes = await _captureWidgetAsImage(invoiceWidget,
          targetWidthPixels: 1152, pixelRatio: 6.0);

      final pdfBytes = await _createPdfFromImage(imageBytes);

      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdfBytes,
        name: 'Invoice_${controller.orderNumber}',
        format: PdfPageFormat.a4,
        usePrinterSettings: true,
      );

      Get.log('Image-based PDF print dialog opened successfully');
    } catch (e) {
      Get.log('Image-based PDF printing error: $e');
      rethrow;
    }
  }

  /// Capture a Flutter widget as an image using screenshot package
  Future<Uint8List> _captureWidgetAsImage(
    Widget widget, {
    required int targetWidthPixels,
    double pixelRatio = 3.0,
  }) async {
    try {
      Get.log('Starting widget capture using screenshot package');

      final captureContextWidget = widget;

      final imageBytes = await screenshotController.captureFromWidget(
        captureContextWidget,
        delay: const Duration(milliseconds: 100),
        pixelRatio: pixelRatio,
        context: Get.context!,
      );

      Get.log(
          'Widget capture completed successfully - ${imageBytes.length} bytes');
      return imageBytes;
    } catch (e) {
      Get.log('Error capturing widget as image: $e');
      rethrow;
    }
  }

  /// Capture widget specifically for thermal printer (576px width)
  Future<Uint8List> _captureWidgetForThermalPrinter(
    Widget widget, {
    double pixelRatio = 2.0,
  }) async {
    try {
      Get.log('Capturing widget for thermal printer (576px width)');

      final captureContextWidget = widget;

      final imageBytes = await screenshotController.captureFromWidget(
        captureContextWidget,
        delay: const Duration(
            milliseconds: 200), // Slightly longer delay for mobile
        pixelRatio: pixelRatio,
        context: Get.context!,
      );

      Get.log(
          'Thermal printer widget capture completed - ${imageBytes.length} bytes');
      return imageBytes;
    } catch (e) {
      Get.log('Error capturing widget for thermal printer: $e');
      rethrow;
    }
  }

  Future<Uint8List> _createPdfFromImage(Uint8List imageBytes) async {
    final pdf = pw.Document();
    final image = pw.MemoryImage(imageBytes);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(10),
        build: (pw.Context context) {
          return pw.Center(
            child: pw.Image(
              image,
              fit: pw.BoxFit.contain,
            ),
          );
        },
      ),
    );
    return pdf.save();
  }

  Future<void> _printToSinglePrinter(String printerIP, List<int> bytes) async {
    try {
      Get.log('Printing to $printerIP');
      const port = 9100;
      final printer = PrinterNetworkManager(printerIP, port: port);
      final result = await printer.printTicket(bytes);
      if (result == PosPrintResult.success) {
        Get.log('Successfully printed to $printerIP');
      } else {
        throw Exception('Failed to print to printer $printerIP: $result');
      }
    } catch (e) {
      Get.log('Error printing to $printerIP: $e');
      rethrow;
    }
  }

  Future<void> _printInvoiceMobile(PrintController controller) async {
    final printersController = Get.find<PrintersIPAddressController>();
    final printerIPs = printersController.getAllPrinterIPs();

    if (printerIPs.isEmpty) {
      Get.snackbar(
        'Error'.tr,
        'No printers configured. Please add printer IPs in settings.'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return;
    }

    Get.log(
        'Starting image-based print job to ${printerIPs.length} printer(s): $printerIPs');

    try {
      // Create the invoice widget
      final invoiceWidget = Invoice(controller: controller);

      // Capture the widget as an image optimized for thermal printer
      final imageBytes = await _captureWidgetForThermalPrinter(
        invoiceWidget,
        pixelRatio: 2.0, // Good balance between quality and file size
      );

      // Generate ESC/POS commands from the image
      final List<int> escPosBytes = await _generateImageBasedEscPos(imageBytes);

      Get.log(
          'Generated ${escPosBytes.length} bytes from image-based invoice for thermal printer');

      // Print to all configured printers
      final List<Future<void>> printTasks = [];
      for (String printerIP in printerIPs) {
        printTasks.add(_printToSinglePrinter(printerIP, escPosBytes));
      }

      try {
        await Future.wait(printTasks);
        Get.snackbar(
          'Success'.tr,
          'Invoice printed to ${printerIPs.length} printer(s) successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.primary,
          colorText: Get.theme.colorScheme.onPrimary,
        );
      } catch (e) {
        Get.snackbar(
          'Warning'.tr,
          'Some printers failed. Check printer connections.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.error,
          colorText: Get.theme.colorScheme.onError,
        );
      }
    } catch (e) {
      Get.log('Mobile print error: $e');
      Get.snackbar(
        'Error',
        'Failed to print invoice: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
    }
  }
}
