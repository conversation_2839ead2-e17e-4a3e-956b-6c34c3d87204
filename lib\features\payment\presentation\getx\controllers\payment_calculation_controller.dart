import 'package:get/get.dart';

import '../../../../../core/widgets/failed_snack_bar.dart';
import '../../../../auth/login/presentation/getx/controllers/login_controller.dart';
import '../../../../settings/presentation/getx/controllers/settings_controller.dart';

class PaymentCalculationController extends GetxController {
  final LoginController loginController = Get.find<LoginController>();
  final SettingsController settingsController = Get.find<SettingsController>();

  // Reactive variables for calculated amounts
  final RxString _shouldPaidAmount = '0.00'.obs;
  final RxDouble _remaining = 0.0.obs;
  final RxDouble _vatAmount = 0.0.obs;

  // Getters
  String get shouldPaidAmount => _shouldPaidAmount.value;
  double get remaining => _remaining.value;
  double get vatAmount => _vatAmount.value;

  // Input data
  num totalPrice = 0;
  RxList<double> methodPaidAmounts = <double>[].obs;

  void initialize(num price) {
    totalPrice = price;
    _calculateAll();
  }

  void updateMethodPaidAmounts(List<double> amounts) {
    methodPaidAmounts.value = amounts;
    _calculateRemaining();
  }

  void _calculateAll() {
    _calculateShouldPaidAmount();
    _calculateRemaining();
  }

  void _calculateShouldPaidAmount() {
    final vatPercentage = _getVatPercentage();

    double total;
    double vat = 0;

    if (settingsController.addTax.value == true) {
      // When addTax is true: ADD VAT to the base price
      final basePrice = totalPrice.toDouble();
      vat = basePrice * (vatPercentage / 100);
      total = basePrice + vat;
      print(
          'DEBUG PAYMENT: Base price: $basePrice, VAT%: $vatPercentage, VAT amount: $vat, Total: $total, addTax: ${settingsController.addTax.value}');
    } else {
      // When addTax is false: Price already includes VAT, use as is
      total = totalPrice.toDouble();
      // Extract VAT from inclusive price
      vat = (total * vatPercentage) / (100 + vatPercentage);
      print(
          'DEBUG PAYMENT: Total price (VAT included): $total, VAT extracted: $vat, addTax: ${settingsController.addTax.value}');
    }

    _shouldPaidAmount.value = total.toStringAsFixed(2);
    _vatAmount.value = vat;
    print(
        'DEBUG PAYMENT: Final should paid amount: ${_shouldPaidAmount.value}, VAT: ${_vatAmount.value}');
  }

  void _calculateRemaining() {
    final vatPercentage = _getVatPercentage();

    double total;
    if (settingsController.addTax.value == true) {
      // When addTax is true: ADD VAT to the base price
      final basePrice = totalPrice.toDouble();
      final vatAmountCalc = basePrice * (vatPercentage / 100);
      total = basePrice + vatAmountCalc;
    } else {
      // When addTax is false: Price already includes VAT, use as is
      total = totalPrice.toDouble();
    }

    double paid = methodPaidAmounts.fold(0.0, (sum, amount) => sum + amount);
    _remaining.value = total - paid;
  }

  double _getVatPercentage() {
    return double.tryParse(loginController.loginTaskModel.data?.company?[0]
                .settings?.invoice?[14].value ??
            '0') ??
        0.0;
  }

  // Validation method
  bool validateTotalPayment() {
    final double totalPaid =
        methodPaidAmounts.fold(0.0, (sum, amount) => sum + amount);
    final double requiredTotal =
        double.tryParse(_shouldPaidAmount.value) ?? 0.0;

    if (totalPaid.toStringAsFixed(2) != requiredTotal.toStringAsFixed(2)) {
      failedSnaskBar(
          'The total paid (${totalPaid.toStringAsFixed(2)}) does not equal the required total (${requiredTotal.toStringAsFixed(2)}). Remaining: ${remaining.toStringAsFixed(2)}');
      return false;
    }
    return true;
  }

  // Calculate amount to send to server (with VAT if needed)
  double calculateServerAmount(double paymentAmount) {
    final vatPercentage = _getVatPercentage();
    double amountToSend = paymentAmount;

    if (settingsController.addTax.value == true) {
      // When addTax is true: Add VAT to the payment amount
      final vatAmountForPayment = paymentAmount * (vatPercentage / 100);
      amountToSend = paymentAmount + vatAmountForPayment;
    }
    // When addTax is false: Use amount as is (already includes VAT)

    return amountToSend;
  }

  @override
  void onInit() {
    // Listen for changes in tax setting
    ever(settingsController.addTax, (_) {
      _calculateAll();
    });

    // Listen for changes in method paid amounts
    ever(methodPaidAmounts, (_) {
      _calculateRemaining();
    });

    super.onInit();
  }
}
