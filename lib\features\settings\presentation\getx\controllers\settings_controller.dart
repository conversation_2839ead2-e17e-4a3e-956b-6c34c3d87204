import 'package:get/get.dart';

class SettingsController extends GetxController {
  final RxBool addPrinter = false.obs;
  final RxBool showTaxingSettings = false.obs;
  final RxBool addTax = false.obs;

  void toggleAddPrinter() {
    addPrinter.value = !addPrinter.value;
  }

  void toggleTaxingSettings() {
    showTaxingSettings.value = !showTaxingSettings.value;
  }

  void toggleAddTax() {
    addTax.value = !addTax.value;
  }
}
