import 'package:get/get.dart';
import 'package:point_of_sale/features/payment/presentation/getx/controllers/pay_order_controller.dart';
import 'package:point_of_sale/features/payment/presentation/getx/controllers/payment_methods_controller.dart';

import '../../../../../core/routes/app_pages.dart';
import '../../../../../core/widgets/failed_snack_bar.dart';
import 'payment_calculation_controller.dart';
import 'payment_method_controller.dart';

class PaymentValidationController extends GetxController {
  final PaymentMethodsController paymentMethodsController =
      Get.find<PaymentMethodsController>();
  final PayOrderController payOrderController = Get.find<PayOrderController>();
  final PaymentCalculationController calculationController =
      Get.find<PaymentCalculationController>();
  final PaymentMethodController methodController =
      Get.find<PaymentMethodController>();

  // Order information
  int orderId = 0;
  int orderNumber = 0;
  num totalPrice = 0;

  void initialize(int id, int number, num price) {
    orderId = id;
    orderNumber = number;
    totalPrice = price;
  }

  Future<void> validateAndProcessPayment() async {
    // Update method paid amount before validation
    methodController.updateMethodPaidAmount();

    // Validate payment totals
    if (!calculationController.validateTotalPayment()) {
      return;
    }

    // Check if there are any payments
    bool hasPayments = false;
    for (int i = 0;
        i < paymentMethodsController.paymentMethodsModel.length;
        i++) {
      final double amount = methodController.methodPaidAmounts[i];
      if (amount > 0) {
        hasPayments = true;
        break;
      }
    }

    if (!hasPayments) {
      failedSnaskBar('Please enter payment amounts for at least one method');
      return;
    }

    // Process payments
    await _processPayments();

    // Navigate to print view
    await _navigateToPrint();
  }

  Future<void> _processPayments() async {
    for (int i = 0;
        i < paymentMethodsController.paymentMethodsModel.length;
        i++) {
      final double amount = methodController.methodPaidAmounts[i];
      if (amount > 0) {
        // Calculate amount to send to server (with VAT if needed)
        final double amountToSend =
            calculationController.calculateServerAmount(amount);

        await payOrderController.payOrder(
          orderId.toString(),
          orderNumber.toString(),
          paymentMethodsController.paymentMethodsModel[i].id.toString(),
          amountToSend,
        );
      }
    }
  }

  Future<void> _navigateToPrint() async {
    await Get.toNamed(
      Routes.print,
      arguments: {
        'orderId': orderId,
        'orderNumber': orderNumber,
        'totalPrice': totalPrice,
      },
    );
  }
}
