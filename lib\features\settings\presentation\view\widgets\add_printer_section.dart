import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/utils/app_colors.dart';
import 'package:point_of_sale/core/utils/app_text_style.dart';
import 'package:point_of_sale/core/widgets/failed_snack_bar.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';

import '../../../../../core/utils/size_config.dart';

class AddPrinterSection extends GetView<PrintersIPAddressController> {
  const AddPrinterSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(
          AppSize.height(16),
        ),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                controller: controller.ipController,
                decoration: InputDecoration(
                  labelText: 'printerIPAddress'.tr,
                  hintText: '${'e.g.'.tr} *************',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
            SizedBox(width: AppSize.width(12)),
            ElevatedButton(
              onPressed: () {
                final ip = controller.ipController.text.trim();
                if (controller.isValidIP(ip)) {
                  controller.addPrinterIP(ip);
                  controller.ipController.clear();
                } else {
                  failedSnaskBar('pleaseEnterAvalidIPAddress'.tr);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                foregroundColor: AppColors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: AppSize.width(12),
                  vertical: AppSize.height(10),
                ),
                child: Text(
                  'add'.tr,
                  style: AppTextStyle.primary14800.copyWith(
                    color: AppColors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
