stages:
  - build
  - docker
  # - deploy

variables:
  FLUTTER_VERSION: "3.32.0"  # Specify your Flutter version here

before_script:
  - apt-get update
  - apt-get install -y curl git unzip
  # - git config --global --add safe.directory /builds/4sharks/pos/point-of-sale-flutter
  - curl -LO "https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_$FLUTTER_VERSION-stable.tar.xz"
  # - curl -LO "https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_$FLUTTER_VERSION-stable.tar.xz"
  # - curl -LO "https://storage.googleapis.com/download/flutter_infra/releases/releases/stable/linux/flutter_linux_$FLUTTER_VERSION-stable.tar.xz" || { echo "Download failed"; exit 1; }
  - tar -xf "flutter_linux_$FLUTTER_VERSION-stable.tar.xz" || { echo "Extracting failed"; exit 1; }
  - export PATH="$PATH:$CI_PROJECT_DIR/flutter/bin"

build:
  stage: build
  script:
    - flutter pub get
    - flutter build web --release
  artifacts:
    paths:
      - build/web

docker_build:
  stage: docker
  image: docker:latest
  services:
    - docker:dind
  variables:
    IMAGE_NAME: mwageeeh/pos-flutter
    IMAGE_TAG: latest
  before_script:
    - echo "Docker Cerdientail"
    - docker login -u $DOCKER_USER -p $DOCKER_PASSWORD
  script:
    - echo "Start Docker Build"
    - docker build -t $IMAGE_NAME:$IMAGE_TAG .
    - docker push $IMAGE_NAME:$IMAGE_TAG
  only:
    - main

# deploy:
#   stage: deploy
#   script:
#     - echo "Deploying to your server..."
#     - docker run -d -p 80:80 $IMAGE_NAME
#   only:
#     - main