import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/utils/app_colors.dart';
import 'package:point_of_sale/core/utils/app_text_style.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/settings_controller.dart';

import '../../../../../core/utils/size_config.dart';

class SettingsAddTexSection extends GetView<SettingsController> {
  const SettingsAddTexSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => InkWell(
        onTap: controller.toggleTaxingSettings,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          height: controller.showTaxingSettings.isTrue
              ? AppSize.height(110)
              : AppSize.height(50),
          padding: EdgeInsets.symmetric(
            horizontal: AppSize.width(16),
            vertical: AppSize.height(12),
          ),
          margin: EdgeInsets.symmetric(
            horizontal: AppSize.width(16),
            vertical: AppSize.height(12),
          ),
          decoration: BoxDecoration(
            color: AppColors.primaryWithOpacity,
            borderRadius: BorderRadius.circular(12),
          ),
          curve: Curves.easeInOut,
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'taxingSettings'.tr,
                    style: AppTextStyle.primary18800,
                  ),
                  InkWell(
                    onTap: controller.toggleTaxingSettings,
                    borderRadius: BorderRadius.circular(8),
                    child: AnimatedRotation(
                      turns: controller.showTaxingSettings.value ? 0.25 : 0.0,
                      duration: const Duration(milliseconds: 200),
                      child: Icon(
                        Icons.keyboard_arrow_right,
                        color: AppColors.primaryColor,
                        size: AppSize.height(24),
                      ),
                    ),
                  ),
                ],
              ),
              if (controller.showTaxingSettings.value) ...[
                SizedBox(
                  height: AppSize.height(16),
                ),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        'addTax'.tr,
                        style: AppTextStyle.primary16800,
                      ),
                    ),
                    Switch(
                      value: controller.addTax.value,
                      onChanged: (value) => controller.toggleAddTax(),
                      activeColor: AppColors.primaryColor,
                    ),
                  ],
                )
              ],
            ],
          ),
        ),
      ),
    );
  }
}
