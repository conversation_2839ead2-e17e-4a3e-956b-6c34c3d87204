import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/utils/app_colors.dart';
import 'package:point_of_sale/core/utils/app_text_style.dart';
import 'package:point_of_sale/core/widgets/failed_snack_bar.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/settings_controller.dart';

import '../../../../core/utils/size_config.dart';

class SettingsPage extends GetView<SettingsController> {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'settings'.tr,
          style: AppTextStyle.primary18800,
        ),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: EdgeInsets.all(
              AppSize.height(20),
            ),
            margin: EdgeInsets.all(
              AppSize.height(20),
            ),
            decoration: BoxDecoration(
              color: AppColors.primaryWithOpacity,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'printerSettings'.tr,
                  style: AppTextStyle.primary18800.copyWith(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                // Add toggle button for add printer section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'addNewPrinter'.tr,
                      style: AppTextStyle.primary16800.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Obx(() => Switch(
                          value: controller.addPrinter.value,
                          onChanged: (value) => controller.toggleAddPrinter(),
                          activeColor: AppColors.primaryColor,
                        )),
                  ],
                ),
                const SizedBox(height: 16),
                // Conditionally show add printer section
                Obx(() => controller.addPrinter.value
                    ? const AddPrinterSection()
                    : const SizedBox.shrink()),
                const SizedBox(height: 24),
                Text(
                  'configuredPrinters'.tr,
                  style: AppTextStyle.primary16800.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                const PrintersList(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class AddPrinterSection extends GetView<PrintersIPAddressController> {
  const AddPrinterSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: controller.ipController,
                    decoration: InputDecoration(
                      labelText: 'printerIPAddress'.tr,
                      hintText: '${'e.g.'.tr}, *************',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: () {
                    final ip = controller.ipController.text.trim();
                    if (controller.isValidIP(ip)) {
                      controller.addPrinterIP(ip);
                      controller.ipController.clear();
                    } else {
                      failedSnaskBar(
                        'pleaseEnterAvalidIPAddress'.tr,
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: Text('add'.tr),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class PrintersList extends GetView<PrintersIPAddressController> {
  const PrintersList({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.printerIPs.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.print_disabled,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'noPrintersConfigured'.tr,
                style: AppTextStyle.primary16800.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'addAPrinterIPAddressAboveToGetStarted'.tr,
                style: AppTextStyle.primary12700.copyWith(
                  color: Colors.grey[500],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        shrinkWrap: true,
        itemCount: controller.printerIPs.length,
        itemBuilder: (context, index) {
          final ip = controller.printerIPs[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: const Icon(
                Icons.print,
                color: AppColors.primaryColor,
              ),
              title: Text(
                ip,
                style: AppTextStyle.primary16800,
              ),
              subtitle: Text(
                'networkPrinter'.tr,
                style: AppTextStyle.primary12700.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(
                      Icons.wifi_tethering,
                      color: Colors.blue,
                    ),
                    onPressed: () async {
                      await controller.testPrinterConnection(ip);
                    },
                    tooltip: 'testConnection'.tr,
                  ),
                  IconButton(
                    icon: const Icon(
                      Icons.delete,
                      color: Colors.red,
                    ),
                    onPressed: () {
                      Get.dialog(
                        AlertDialog(
                          title: const Text('Delete Printer'),
                          content: Text(
                              '${'areYouSureYouWantToRemoveThisPrinter'}.tr}$ip${'?'.tr}'),
                          actions: [
                            TextButton(
                              onPressed: () => Get.back(),
                              child: Text('cancel'.tr),
                            ),
                            TextButton(
                              onPressed: () {
                                controller.removePrinterIP(ip);
                                Get.back();
                              },
                              style: TextButton.styleFrom(
                                foregroundColor: Colors.red,
                              ),
                              child: Text('delete'.tr),
                            ),
                          ],
                        ),
                      );
                    },
                    tooltip: 'deletePrinter'.tr,
                  ),
                ],
              ),
            ),
          );
        },
      );
    });
  }
}
