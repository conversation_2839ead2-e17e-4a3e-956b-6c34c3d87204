import 'package:get/get.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/settings_controller.dart';

import '../../../../../core/services/cash_data_source.dart';
import '../controllers/printers_ip_address_controller.dart';

class SettingsBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => CashDataSource());
    Get.lazyPut<PrintersIPAddressController>(
      () => PrintersIPAddressController(),
    );
    Get.lazyPut<SettingsController>(
      () => SettingsController(),
    );
  }
}
