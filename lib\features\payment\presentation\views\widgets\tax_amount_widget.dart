import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';
import '../../getx/controllers/payment_controller.dart';

class TaxAmountWidget extends GetView<PaymentController> {
  const TaxAmountWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final vatPercentage = double.tryParse(controller
                  .loginController
                  .loginTaskModel
                  .data
                  ?.company?[0]
                  .settings
                  ?.invoice?[14]
                  .value ??
              '0') ??
          0;

      final basePrice = controller.totalPrice;
      final addTax = controller.settingsController.addTax.value;

      double taxAmount = 0;
      String taxLabel = '';
      Color taxColor = Colors.grey;

      if (addTax) {
        // When addTax is true: Calculate VAT on base price
        taxAmount = basePrice * (vatPercentage / 100);
        taxLabel = 'VAT (${vatPercentage.toStringAsFixed(0)}%)';
        taxColor = Colors.green;
      } else {
        // When addTax is false: Extract VAT from inclusive price
        taxAmount = (basePrice * vatPercentage) / (100 + vatPercentage);
        taxLabel = 'VAT Included (${vatPercentage.toStringAsFixed(0)}%)';
        taxColor = Colors.blue;
      }

      if (taxAmount <= 0) {
        return const SizedBox.shrink();
      }

      return Container(
        margin: EdgeInsets.only(bottom: AppSize.height(8)),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border:
              Border.all(color: taxColor.withValues(alpha: 0.3), width: 1.5),
          color: taxColor.withValues(alpha: 0.1),
        ),
        height: AppSize.height(55),
        width: double.infinity,
        padding: EdgeInsets.symmetric(
          horizontal: AppSize.width(16),
          vertical: AppSize.height(9),
        ),
        child: Row(
          children: [
            Icon(
              addTax ? Icons.add_circle_outline : Icons.info_outline,
              size: 20,
            ),
            const Spacer(),
            Text(
              "${taxAmount.toStringAsFixed(2)} ${'SR'.tr}",
              style: AppTextStyle.primary16800,
            ),
          ],
        ),
      );
    });
  }
}
